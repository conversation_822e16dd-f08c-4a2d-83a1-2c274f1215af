# 图鉴武器无限制领取功能修改说明

## 修改目标
将玉帝版后台的图鉴系统修改为：
- 所有武器图鉴都显示领取按钮
- 无需任何前置条件即可领取武器
- 可以无限次重复领取同一武器
- 显示已领取次数

## 问题分析

### 原有问题
1. **图鉴获取限制不一致**：后台版本绕过了正常的图鉴获取限制，但仍调用标记方法
2. **重复获取问题**：图鉴状态与实际拥有武器不匹配
3. **前置条件限制**：需要满足特定条件才能显示领取按钮

### 核心逻辑
图鉴系统的获取流程：
1. `ArmsDefine.getBookCanGet()` - 检查是否可以获取（前置条件）
2. `PlayerMainData.canBookGetB()` - 检查获取次数限制
3. `HelperBookBox` - 界面显示和交互逻辑

## 修改步骤

### 1. 修改武器图鉴定义 - 移除前置条件

**文件**: `scripts玉帝后台版\dataAll\arms\define\ArmsDefine.as`

**位置**: 第870-873行

**原代码**:
```actionscript
public function getBookCanGet() : Boolean
{
   return this.bookCanGetB;
}
```

**修改为**:
```actionscript
public function getBookCanGet() : Boolean
{
   // 玉帝版：所有武器图鉴都可以获取，无需前置条件
   return true;
}
```

**说明**: 让所有武器图鉴都返回可获取状态，移除前置条件检查。

### 2. 修改玩家数据 - 移除获取次数限制

**文件**: `scripts玉帝后台版\dataAll\_player\base\PlayerMainData.as`

**位置**: 第378-381行

**原代码**:
```actionscript
public function canBookGetB(id0:String) : Boolean
{
   return this.getBookGetNum(id0) < 1;
}
```

**修改为**:
```actionscript
public function canBookGetB(id0:String) : Boolean
{
   // 玉帝版：允许无限次获取图鉴武器
   return true;
}
```

**说明**: 移除"只能获取一次"的限制，允许无限次重复获取。

### 3. 修改图鉴界面 - 优化按钮逻辑

**文件**: `scripts玉帝后台版\UI\helper\book\HelperBookBox.as`

#### 3.1 修改按钮点击逻辑

**位置**: 第119-147行

**原代码**:
```actionscript
override protected function btnClick(e:MouseEvent) : void
{
   var id0:String = null;
   var gift0:GiftAddDefine = null;
   var g0:GiftAddDefineGroup = null;
   var bb0:Boolean = false;
   var btn0:NormalBtn = e.target as NormalBtn;
   if(btn0 == this.closeBtn)
   {
      hide();
   }
   else if(btn0 == this.getterBtn)
   {
      id0 = this.nowDefine.getBookId();
      gift0 = this.nowDefine.getBookGift();
      if(mainData.canBookGetB(id0))
      {
         // 移除重复拥有检查，允许重复获取图鉴武器
         // 这样图鉴获取功能就和刷指定武器功能保持一致
         g0 = new GiftAddDefineGroup();
         g0.addGift(gift0);
         bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"领取成功！");
         if(bb0)
         {
            mainData.getBookIdEvent(id0);
            this.fleshBtn();
         }
      }
   }
}
```

**修改为**:
```actionscript
override protected function btnClick(e:MouseEvent) : void
{
   var id0:String = null;
   var gift0:GiftAddDefine = null;
   var g0:GiftAddDefineGroup = null;
   var bb0:Boolean = false;
   var btn0:NormalBtn = e.target as NormalBtn;
   if(btn0 == this.closeBtn)
   {
      hide();
   }
   else if(btn0 == this.getterBtn)
   {
      id0 = this.nowDefine.getBookId();
      gift0 = this.nowDefine.getBookGift();
      // 玉帝版：直接获取，无需检查限制
      g0 = new GiftAddDefineGroup();
      g0.addGift(gift0);
      bb0 = GiftAddit.addAndAutoBagSpacePan(g0,"领取成功！");
      if(bb0)
      {
         // 记录获取次数，但不限制重复获取
         mainData.getBookIdEvent(id0);
         this.fleshBtn();
      }
   }
}
```

**说明**: 移除获取限制检查，直接执行获取操作。

#### 3.2 修改按钮显示逻辑

**位置**: 第192-215行

**原代码**:
```actionscript
private function fleshBtn() : void
{
   var getNum0:int = 0;
   var canB0:Boolean = Boolean(this.nowDefine.getBookCanGet());
   if(canB0)
   {
      this.getterBtn.visible = true;
      if(mainData.canBookGetB(this.nowDefine.getBookId()))
      {
         getNum0 = mainData.getBookGetNum(this.nowDefine.getBookId());
         this.getterBtn.actived = true;
         this.getterBtn.setName(getNum0 >= 0 ? "再次领取" : "领取");
      }
      else
      {
         this.getterBtn.actived = false;
         this.getterBtn.setName("已领取");
      }
   }
   else
   {
      this.getterBtn.visible = false;
   }
}
```

**修改为**:
```actionscript
private function fleshBtn() : void
{
   var getNum0:int = 0;
   var canB0:Boolean = Boolean(this.nowDefine.getBookCanGet());
   if(canB0)
   {
      this.getterBtn.visible = true;
      // 玉帝版：按钮始终可用，显示获取次数
      getNum0 = mainData.getBookGetNum(this.nowDefine.getBookId());
      this.getterBtn.actived = true;
      if(getNum0 >= 0)
      {
         this.getterBtn.setName("领取(" + (getNum0 + 1) + "次)");
      }
      else
      {
         this.getterBtn.setName("领取");
      }
   }
   else
   {
      this.getterBtn.visible = false;
   }
}
```

**说明**: 按钮始终可用，显示当前获取次数信息。

### 4. 同步修改3464版本

为保持一致性，对 `scripts3464` 目录下的相同文件进行相同修改：

1. `scripts3464\dataAll\arms\define\ArmsDefine.as`
2. `scripts3464\dataAll\_player\base\PlayerMainData.as`
3. `scripts3464\UI\helper\book\HelperBookBox.as`

## 修改后的功能特点

### ✅ 实现的功能
- **所有武器图鉴都有领取按钮** - 不再需要满足特定条件
- **无限次领取** - 可以重复获取同一个武器
- **显示获取次数** - 按钮会显示已获取的次数（如"领取(3次)"）
- **保持原有功能** - 仍然会记录图鉴获取状态和次数

### 🔧 技术细节
- 保留了获取次数记录功能，便于统计和调试
- 移除了所有获取限制，包括前置条件和次数限制
- 优化了用户界面显示，提供更好的用户体验
- 保持了代码的向后兼容性

### 📝 使用说明
1. 打开游戏图鉴界面
2. 选择任意武器图鉴
3. 点击"领取"按钮即可获得武器
4. 可以重复点击获取多个相同武器
5. 按钮会显示已获取的次数

## 注意事项

1. **备份原文件**: 修改前建议备份原始文件
2. **测试功能**: 修改后需要测试图鉴功能是否正常
3. **版本同步**: 确保所有相关版本都进行了相同修改
4. **性能考虑**: 无限制获取可能导致背包物品过多，注意背包容量

## 相关文件列表

```
scripts玉帝后台版\
├── dataAll\
│   ├── arms\define\ArmsDefine.as
│   └── _player\base\PlayerMainData.as
└── UI\helper\book\HelperBookBox.as

scripts3464\
├── dataAll\
│   ├── arms\define\ArmsDefine.as
│   └── _player\base\PlayerMainData.as
└── UI\helper\book\HelperBookBox.as
```

## 后续修改：删除自定义后台中的图鉴武器刷取指令

### 修改原因
由于图鉴系统已经支持无限制领取，自定义后台中的图鉴武器刷取指令变得冗余，为了避免功能重复和混淆，删除了相关指令。

### 删除的功能
1. **AddBookArmByName方法** - 添加指定图鉴武器
2. **AddBookArms方法** - 添加所有图鉴武器
3. **AddBookArmsByCategory方法** - 按分类刷图鉴武器
4. **相关批处理变量** - 异步批量添加相关的变量

### 修改的文件

#### 玉帝版后台
- `scripts玉帝后台版\UI\setting\SettingGamingBox.as`
  - 删除了AddBookArmByName、AddBookArms、AddBookArmsByCategory方法
  - 删除了批处理相关变量
  - 修改了AddArmsCheating方法，移除图鉴武器选项（03、04、05）
  - 修改了ThingCheating方法中的武器类提示文本

#### 3464版本
- `scripts3464\UI\setting\SettingGamingBox.as`
  - 删除了AddBookArmByName、AddBookArms、AddBookArmsByCategory方法
  - 修改了AddArmsCheating方法，移除图鉴武器选项（03、04、05）
  - 修改了ThingCheating方法中的武器类提示文本

### 修改后的效果
- 自定义后台武器类选项简化为：
  - `[00]` 添加指定武器
  - `[01]` 添加所有稀有武器
  - `[02]` 添加所有黑色武器
  - `[03]` 添加所有武器碎片
- 添加了提示信息："注意：图鉴武器请直接在图鉴界面领取"
- 避免了功能重复，用户体验更加清晰

---

**修改完成日期**: 2025-08-03
**修改版本**: 玉帝版后台 + 3464版本
**修改目的**: 提供便捷的图鉴武器获取功能，用于测试和调试，并清理冗余的后台指令
